CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
/usr/bin/ld.gold : erreur : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so : impossible de charger le greffon de bibliothèque : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/../lib/LLVMgold.so: Ne peut ouvrir le fichier d'objet partagé: Aucun fichier ou dossier de ce nom
/usr/bin/ld.gold : erreur fatale : /home/<USER>/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/21/crtbegin_dynamic.o : numéro de machine ELF 40 non pris en charge
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.


