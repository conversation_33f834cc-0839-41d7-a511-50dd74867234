# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: lto-test
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/_CMakeLTOTest-CXX/bin/
# =============================================================================
# Object build statements for STATIC_LIBRARY target foo


#############################################
# Order-only phony target for foo

build cmake_object_order_depends_target_foo: phony || CMakeFiles/foo.dir

build CMakeFiles/foo.dir/foo.cpp.o: CXX_COMPILER__foo_ /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/_CMakeLTOTest-CXX/src/foo.cpp || cmake_object_order_depends_target_foo
  DEP_FILE = CMakeFiles/foo.dir/foo.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -fPIC
  OBJECT_DIR = CMakeFiles/foo.dir
  OBJECT_FILE_DIR = CMakeFiles/foo.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target foo


#############################################
# Link the static library libfoo.a

build libfoo.a: CXX_STATIC_LIBRARY_LINKER__foo_ CMakeFiles/foo.dir/foo.cpp.o
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin
  OBJECT_DIR = CMakeFiles/foo.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = libfoo.a
  TARGET_PDB = foo.a.dbg

# =============================================================================
# Object build statements for EXECUTABLE target boo


#############################################
# Order-only phony target for boo

build cmake_object_order_depends_target_boo: phony || cmake_object_order_depends_target_foo

build CMakeFiles/boo.dir/main.cpp.o: CXX_COMPILER__boo_ /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/_CMakeLTOTest-CXX/src/main.cpp || cmake_object_order_depends_target_boo
  DEP_FILE = CMakeFiles/boo.dir/main.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -fPIE
  OBJECT_DIR = CMakeFiles/boo.dir
  OBJECT_FILE_DIR = CMakeFiles/boo.dir


# =============================================================================
# Link build statements for EXECUTABLE target boo


#############################################
# Link the executable boo

build boo: CXX_EXECUTABLE_LINKER__boo_ CMakeFiles/boo.dir/main.cpp.o | libfoo.a || libfoo.a
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin
  LINK_FLAGS = -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold
  LINK_LIBRARIES = libfoo.a  -latomic -lm
  OBJECT_DIR = CMakeFiles/boo.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = boo
  TARGET_PDB = boo.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/_CMakeLTOTest-CXX/bin && /home/<USER>/Android/Sdk/cmake/3.22.1/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/_CMakeLTOTest-CXX/bin && /home/<USER>/Android/Sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/_CMakeLTOTest-CXX/src -B/media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/_CMakeLTOTest-CXX/bin
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build foo: phony libfoo.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/_CMakeLTOTest-CXX/bin

build all: phony libfoo.a boo

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/_CMakeLTOTest-CXX/src/CMakeLists.txt CMakeCache.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake /home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake /media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86/CMakeFiles/_CMakeLTOTest-CXX/src/CMakeLists.txt CMakeCache.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
