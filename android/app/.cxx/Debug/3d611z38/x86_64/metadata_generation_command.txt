                        -H/media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/27.1.12297006
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/build/intermediates/cxx/Debug/3d611z38/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/build/intermediates/cxx/Debug/3d611z38/obj/x86_64
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=/media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/prefab/x86_64/prefab
-B/media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/.cxx/Debug/3d611z38/x86_64
-GNinja
-DPROJECT_BUILD_DIR=/media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android/app/build
-DPROJECT_ROOT_DIR=/media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/android
-DREACT_ANDROID_DIR=/media/soufian-ch/P1/projects/mkadia-app/mkadia-mobile/node_modules/react-native/ReactAndroid
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2