{"name": "mkadia", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "android:dev": "ENVFILE=.env.development react-native run-android", "android:staging": "ENVFILE=.env.staging react-native run-android", "android:prod": "ENVFILE=.env.production react-native run-android", "ios": "react-native run-ios", "ios:dev": "ENVFILE=.env.development react-native run-ios", "ios:staging": "ENVFILE=.env.staging react-native run-ios", "ios:prod": "ENVFILE=.env.production react-native run-ios", "start": "react-native start", "start:dev": "ENVFILE=.env.development react-native start", "start:staging": "ENVFILE=.env.staging react-native start", "start:prod": "ENVFILE=.env.production react-native start", "lint": "eslint .", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native/new-app-screen": "0.81.0", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.25", "axios": "^1.11.0", "lucide-react-native": "^0.542.0", "react": "19.1.0", "react-native": "0.81.0", "react-native-bootsplash": "^6.3.10", "react-native-dotenv": "^3.4.11", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.15.3", "react-native-svg": "^15.12.1", "rxjs": "^7.8.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.0", "@react-native/eslint-config": "0.81.0", "@react-native/metro-config": "0.81.0", "@react-native/typescript-config": "0.81.0", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-flipper": "^0.273.0", "react-test-renderer": "19.1.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18"}}