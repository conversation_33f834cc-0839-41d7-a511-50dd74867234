import axios from "axios";
import { from, Observable } from "rxjs";
import { map, catchError } from "rxjs/operators";
import { ProductCard } from "../models/ProductCard";
const API_URL = "http://*************:8080/mkadia-api/api/v1/public/products/best-seller";

// Création d’un observable pour un GET
export const getProducts$ = () : Observable<ProductCard[]> => {
  return from(axios.get(API_URL)).pipe(
    map((response) => response.data), // extraire uniquement les données
    catchError((error) => {
      throw new Error(error);
    })
  );
};
