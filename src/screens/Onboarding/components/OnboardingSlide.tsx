// src/components/Onboarding/OnboardingSlide.tsx
import React from "react";
import { View, Text, Image, StyleSheet, Dimensions } from "react-native";

const { width, height } = Dimensions.get("window");

interface SlideProps {
  title: string;
  description: string;
  image: string;
}

const OnboardingSlide: React.FC<SlideProps> = ({ title, description, image }) => {
  return (
    <View style={[styles.slide, { width }]}>
      <Image source={{ uri: image }} style={styles.image} />
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.description}>{description}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  slide: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  image: {
    width: width * 0.6,
    height: height * 0.35,
    resizeMode: "contain",
    marginBottom: 30,
  },
  title: {
    fontSize: 26,
    fontFamily: "Raleway-Bold",
    color: "#4E2A84",
    textAlign: "center",
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    fontFamily: "Poppins-Regular",
    textAlign: "center",
    color: "#5D3B8D",
    paddingHorizontal: 20,
  },
});

export default OnboardingSlide;
